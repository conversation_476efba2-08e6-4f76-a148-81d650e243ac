"use client";

import Hero4 from '@/components/Heros/Hero4';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import Values from '@/components/Values';
import TitleTextImages from '@/components/TitleTextImages';
import Description from '@/components/Description';


export default function AgencyClient({ params }) {
  const locale = params.locale || 'fr';

  // Données des expertises (en attendant une meilleure solution de traduction)
  const expertisesData = locale === 'fr' ? [
    {
      title: "Création et refonte d'identité",
      items: [
        "Création de logotype",
        "Design de marchandises",
        "Impression sur-mesure"
      ]
    },
    {
      title: "Stratégie et design thinking",
      items: [
        "Analyse concurrentielle",
        "Définition de personas",
        "Parcours utilisateur"
      ]
    },
    {
      title: "Développement web",
      items: [
        "Sites vitrine",
        "E-commerce",
        "Applications web"
      ]
    }
  ] : [
    {
      title: "Brand identity creation and redesign",
      items: [
        "Logo creation",
        "Merchandise design",
        "Custom printing"
      ]
    },
    {
      title: "Strategy and design thinking",
      items: [
        "Competitive analysis",
        "Persona definition",
        "User journey"
      ]
    },
    {
      title: "Web development",
      items: [
        "Showcase websites",
        "E-commerce",
        "Web applications"
      ]
    }
  ];

  return (
    <div>
      <Hero4 
        locale={locale}
        imgSrc="/images/lucas-joliveau-siege-ordinateur-portable.png"
      />
      
      <ExpertiseAccordion
        title={locale === 'fr' ? 'Nos expertises' : 'Our expertise'}
        expertises={expertisesData}
      />

      <ThreeColumnTitle locale={locale} />
      <TitleTextImages locale={locale} />
      <Description
        descriptionTitle="Un lien tissé serré avec nos clients"
        descriptionText={
          <>
            De la première prise de contact jusqu'à la livraison, Lucas, le fondateur de l'agence est votre point de contact unique.
            <br /><br />
            Quand un projet le demande, Kapreon collabore avec des partenaires locaux spécialisés que nous connaissons et en qui nous avons confiance.
            <br /><br />
            Contrairement aux grosses agences, nous plus de clarté, des processus plus fluide, plus de réactivité, et surtout une vraie continuité dans l'exécution du projet.
          </>
        }
        showButton={false}
        titleTag="h3"
      />
      <Values />
      <StatsCards />
      <Testimonials locale={locale} />
    </div>
  );
}
