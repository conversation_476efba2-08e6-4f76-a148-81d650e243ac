/* ─────────── Hero4 Component ─────────── */

.hero {
  padding: 2rem 0;

  @media (max-width: 768px) {
    padding: 1rem 0;
  }
}



/* ─────────── Contenu principal ─────────── */

.content {
  display: flex;
  gap: 4rem;
  align-items: flex-end;
  padding: 4rem 0;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 2rem;
    align-items: stretch;
    padding: 2rem 0;
  }
}

.textBlock {
  flex: 0 0 40%;
  max-width: 40%;

  @media (max-width: 768px) {
    flex: 1;
    max-width: 100%;
  }
}

.description {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 400;
}

/* ─────────── Image avec clip-path ─────────── */

.imageContainer {
  flex: 0 0 50%;
  position: relative;
  aspect-ratio: 4/3;

  @media (max-width: 768px) {
    flex: 1;
    aspect-ratio: 16/10;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  transform-origin: top left; /* Point d'origine pour l'agrandissement */
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* ─────────── Responsive adjustments ─────────── */

@media (min-width: 769px) and (max-width: 1024px) {
  .hero {
    padding: 3rem 0 5rem;
  }
  
  .content {
    gap: 3rem;
  }
  
  .textBlock {
    flex: 0 0 45%;
    max-width: 45%;
  }
  
  .imageContainer {
    flex: 0 0 45%;
  }
}

@media (min-width: 1025px) {
  .hero {
    padding: 6rem 0 8rem;
  }
  
  .subtitleContainer {
    margin-left: 6rem;
  }
  
  .titleLine2Container {
    margin-left: 3rem;
  }
  
  .subtitleContinuation {
    margin-left: 3rem;
  }
}

/* ─────────── Animation states ─────────── */

.imageWrapper {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

/* ─────────── Accessibility ─────────── */

@media (prefers-reduced-motion: reduce) {
  .imageWrapper {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}
