/* ─────────── Hero4 Component ─────────── */

.hero {
  padding: 4rem 0 6rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4rem;

  @media (max-width: 768px) {
    padding: 2rem 0 4rem;
    gap: 3rem;
    min-height: auto;
  }
}

/* ─────────── Header avec titres ─────────── */

.header {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (max-width: 768px) {
    gap: 1.5rem;
  }
}

.titleContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.titleLine1 {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: #1a1a1a;
  margin: 0;
}

.titleLine2Container {
  margin-left: 2rem;

  @media (max-width: 768px) {
    margin-left: 1rem;
  }
}

.titleLine2 {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: #1a1a1a;
  margin: 0;
}

/* ─────────── Sous-titres décalés ─────────── */

.subtitleContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem 1rem;
  align-items: baseline;
  margin-left: 4rem;

  @media (max-width: 768px) {
    margin-left: 2rem;
    flex-direction: column;
    gap: 0.25rem;
  }
}

.subtitleHighlight {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 300;
  color: #999;
  letter-spacing: -0.01em;
}

.subtitleNormal {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 300;
  color: #1a1a1a;
  letter-spacing: -0.01em;
}

.subtitleContinuation {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 300;
  color: #1a1a1a;
  letter-spacing: -0.01em;
  margin-left: 2rem;

  @media (max-width: 768px) {
    margin-left: 1rem;
  }
}

/* ─────────── Contenu principal ─────────── */

.content {
  display: flex;
  gap: 4rem;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 2rem;
    align-items: stretch;
  }
}

.textBlock {
  flex: 0 0 40%;
  max-width: 40%;

  @media (max-width: 768px) {
    flex: 1;
    max-width: 100%;
  }
}

.description {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 400;
}

/* ─────────── Image avec clip-path ─────────── */

.imageContainer {
  flex: 0 0 50%;
  position: relative;
  aspect-ratio: 4/3;

  @media (max-width: 768px) {
    flex: 1;
    aspect-ratio: 16/10;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* ─────────── Responsive adjustments ─────────── */

@media (min-width: 769px) and (max-width: 1024px) {
  .hero {
    padding: 3rem 0 5rem;
  }
  
  .content {
    gap: 3rem;
  }
  
  .textBlock {
    flex: 0 0 45%;
    max-width: 45%;
  }
  
  .imageContainer {
    flex: 0 0 45%;
  }
}

@media (min-width: 1025px) {
  .hero {
    padding: 6rem 0 8rem;
  }
  
  .subtitleContainer {
    margin-left: 6rem;
  }
  
  .titleLine2Container {
    margin-left: 3rem;
  }
  
  .subtitleContinuation {
    margin-left: 3rem;
  }
}

/* ─────────── Animation states ─────────── */

.imageWrapper {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

/* ─────────── Accessibility ─────────── */

@media (prefers-reduced-motion: reduce) {
  .imageWrapper {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}
