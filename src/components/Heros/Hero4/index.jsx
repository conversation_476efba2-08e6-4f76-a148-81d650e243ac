'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import Image from 'next/image';
import TextReveal from '@/components/TextReveal';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './style.module.scss';
import Hero from '@/components/Heros/Hero';


export default function Hero4({
  imgSrc = '/images/lucas-joliveau-siege-ordinateur-portable.png',
  locale = 'fr',
  containerClass = 'container'
}) {
  const { t } = useTranslation('pages');
  const containerRef = useRef(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Animation qui s'agrandit depuis le coin supérieur gauche
  const clipPath = useTransform(
    scrollYProgress,
    [0, 0.5],
    [
      "polygon(0% 0%, 5% 0%, 5% 5%, 0% 5%)", // Très petit carré en haut à gauche
      "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"    // Révélation complète
    ]
  );

  return (
    <section ref={containerRef}>

      <Hero
        title="Une fusée créative pour les entreprises"
        subtitle="Spécialistes du design et du développement web"
        locale={locale}
      />
      {/* Contenu principal avec texte et image */}
      <div className={`${styles.content} container`}>
        {/* Bloc de texte à gauche */}
        <motion.div 
          className={styles.textBlock}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <p className={styles.description}>
            {t('agency.hero4.description')}
          </p>
        </motion.div>

        {/* Image avec animation clip-path */}
        <div className={styles.imageContainer}>
          <motion.div 
            className={styles.imageWrapper}
            style={{ clipPath }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1 }}
          >
            <Image
              src={imgSrc}
              alt={t('agency.hero4.image_alt')}
              fill
              sizes="(max-width: 768px) 100vw, 50vw"
              className={styles.image}
              priority
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
